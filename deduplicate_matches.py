#!/usr/bin/env python3
"""
Product Group Deduplication Script

This script removes duplicate product groups from matches_cleaned.json based on 
product keys (supplier_productNumber format). Two groups are considered duplicates 
if they contain the exact same set of product keys, regardless of order.

Usage: python deduplicate_matches.py
"""

import json
import logging
from typing import List, Dict, Set, Tuple
from collections import defaultdict
import time

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('deduplication.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def load_matches_data(filename: str) -> List[Dict[str, str]]:
    """Load the matches data from JSON file with proper error handling."""
    try:
        logger.info(f"Loading data from {filename}...")
        with open(filename, 'r', encoding='utf-8') as file:
            data = json.load(file)
        logger.info(f"Successfully loaded {len(data)} product groups")
        return data
    except FileNotFoundError:
        logger.error(f"File {filename} not found")
        raise
    except json.JSONDecodeError as e:
        logger.error(f"JSON decode error: {e}")
        raise
    except UnicodeDecodeError as e:
        logger.error(f"Unicode decode error: {e}")
        raise

def get_group_key_signature(group: Dict[str, str]) -> frozenset:
    """
    Create a signature for a product group based on its keys.
    Returns a frozenset of keys that can be used for comparison.
    """
    return frozenset(group.keys())

def deduplicate_groups(groups: List[Dict[str, str]]) -> Tuple[List[Dict[str, str]], Dict[str, int]]:
    """
    Remove duplicate groups based on product keys.
    
    Args:
        groups: List of product group dictionaries
        
    Returns:
        Tuple of (deduplicated_groups, statistics)
    """
    logger.info("Starting deduplication process...")
    
    seen_signatures = set()
    unique_groups = []
    duplicate_count = 0
    signature_counts = defaultdict(int)
    
    start_time = time.time()
    
    for i, group in enumerate(groups):
        # Progress logging for large datasets
        if i % 5000 == 0 and i > 0:
            logger.info(f"Processed {i}/{len(groups)} groups ({i/len(groups)*100:.1f}%)")
        
        # Create signature for this group
        signature = get_group_key_signature(group)
        signature_counts[signature] += 1
        
        # Check if we've seen this signature before
        if signature not in seen_signatures:
            seen_signatures.add(signature)
            unique_groups.append(group)
        else:
            duplicate_count += 1
    
    end_time = time.time()
    processing_time = end_time - start_time
    
    # Calculate statistics
    stats = {
        'original_count': len(groups),
        'unique_count': len(unique_groups),
        'duplicates_removed': duplicate_count,
        'processing_time_seconds': processing_time,
        'duplicate_signatures': sum(1 for count in signature_counts.values() if count > 1),
        'max_duplicates_per_signature': max(signature_counts.values()) if signature_counts else 0
    }
    
    logger.info(f"Deduplication completed in {processing_time:.2f} seconds")
    logger.info(f"Original groups: {stats['original_count']}")
    logger.info(f"Unique groups: {stats['unique_count']}")
    logger.info(f"Duplicates removed: {stats['duplicates_removed']}")
    logger.info(f"Duplicate signatures found: {stats['duplicate_signatures']}")
    logger.info(f"Max duplicates per signature: {stats['max_duplicates_per_signature']}")
    
    return unique_groups, stats

def save_deduplicated_data(data: List[Dict[str, str]], filename: str) -> None:
    """Save the deduplicated data to a JSON file."""
    try:
        logger.info(f"Saving deduplicated data to {filename}...")
        with open(filename, 'w', encoding='utf-8') as file:
            json.dump(data, file, indent=2, ensure_ascii=False)
        logger.info(f"Successfully saved {len(data)} unique groups to {filename}")
    except Exception as e:
        logger.error(f"Error saving file {filename}: {e}")
        raise

def analyze_sample_duplicates(groups: List[Dict[str, str]], max_samples: int = 5) -> None:
    """Analyze and log some sample duplicate groups for verification."""
    logger.info("Analyzing sample duplicates...")
    
    signature_groups = defaultdict(list)
    
    # Group by signature
    for i, group in enumerate(groups):
        signature = get_group_key_signature(group)
        signature_groups[signature].append((i, group))
    
    # Find duplicates and show samples
    duplicate_signatures = {sig: groups for sig, groups in signature_groups.items() if len(groups) > 1}
    
    if duplicate_signatures:
        logger.info(f"Found {len(duplicate_signatures)} unique signatures with duplicates")
        
        sample_count = 0
        for signature, group_list in list(duplicate_signatures.items())[:max_samples]:
            logger.info(f"\nSample duplicate group (signature has {len(group_list)} instances):")
            logger.info(f"Keys: {sorted(signature)}")
            logger.info(f"Group indices: {[idx for idx, _ in group_list]}")
            sample_count += 1
    else:
        logger.info("No duplicates found in sample analysis")

def main():
    """Main function to orchestrate the deduplication process."""
    input_file = 'matches_cleaned.json'
    output_file = 'matches_deduplicated.json'
    
    try:
        # Load data
        groups = load_matches_data(input_file)
        
        # Analyze samples before deduplication
        analyze_sample_duplicates(groups)
        
        # Perform deduplication
        unique_groups, stats = deduplicate_groups(groups)
        
        # Save results
        save_deduplicated_data(unique_groups, output_file)
        
        # Save statistics
        stats_file = 'deduplication_stats.json'
        with open(stats_file, 'w', encoding='utf-8') as f:
            json.dump(stats, f, indent=2)
        logger.info(f"Statistics saved to {stats_file}")
        
        # Final summary
        reduction_percentage = (stats['duplicates_removed'] / stats['original_count']) * 100
        logger.info(f"\n=== DEDUPLICATION SUMMARY ===")
        logger.info(f"Input file: {input_file}")
        logger.info(f"Output file: {output_file}")
        logger.info(f"Original groups: {stats['original_count']:,}")
        logger.info(f"Unique groups: {stats['unique_count']:,}")
        logger.info(f"Duplicates removed: {stats['duplicates_removed']:,}")
        logger.info(f"Reduction: {reduction_percentage:.2f}%")
        logger.info(f"Processing time: {stats['processing_time_seconds']:.2f} seconds")
        
    except Exception as e:
        logger.error(f"Script failed: {e}")
        raise

if __name__ == "__main__":
    main()
