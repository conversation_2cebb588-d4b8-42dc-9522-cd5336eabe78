// Global variables
let priceData = null;
let filteredData = [];
let currentFilters = {
    seller: '',
    search: '',
    status: '',
    proximity: ''
};

// DOM elements
const loadingMessage = document.getElementById('loadingMessage');
const productCards = document.getElementById('productCards');
const resultsInfo = document.getElementById('resultsInfo');
const resultsText = document.getElementById('resultsText');

// Filter elements
const sellerFilter = document.getElementById('sellerFilter');
const searchInput = document.getElementById('searchInput');
const statusFilter = document.getElementById('statusFilter');
const proximityFilter = document.getElementById('proximityFilter');

// Stats elements
const totalGroupsSpan = document.getElementById('totalGroups');
const showingGroupsSpan = document.getElementById('showingGroups');
const closeMatchesSpan = document.getElementById('closeMatches');
const avgVarianceSpan = document.getElementById('avgVariance');

// Load and initialize the application
async function initializeApp() {
    try {
        console.log('Loading price comparison data...');
        const response = await fetch('price_comparison_results.json');

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        priceData = await response.json();
        console.log('Data loaded successfully:', priceData.summary);

        // Initialize the interface
        setupEventListeners();
        updateStatistics();

        // Check if a specific group was selected from search
        const selectedGroupIndex = localStorage.getItem('selectedGroupIndex');
        if (selectedGroupIndex !== null) {
            displaySelectedGroup(parseInt(selectedGroupIndex));
            localStorage.removeItem('selectedGroupIndex'); // Clear after use
        } else {
            filterAndDisplayProducts();
        }

        // Hide loading message and show content
        loadingMessage.classList.add('hidden');
        productCards.classList.remove('hidden');

    } catch (error) {
        console.error('Error loading data:', error);
        loadingMessage.innerHTML = `
            <div style="color: #e74c3c;">
                <h3>Error Loading Data</h3>
                <p>Could not load price comparison results. Please ensure the analysis has been run and the results file exists.</p>
                <p><strong>Error:</strong> ${error.message}</p>
                <button onclick="location.reload()" style="margin-top: 10px; padding: 10px 20px; background: #3498db; color: white; border: none; border-radius: 5px; cursor: pointer;">
                    Retry
                </button>
            </div>
        `;
    }
}

// Setup event listeners for filters
function setupEventListeners() {
    sellerFilter.addEventListener('change', (e) => {
        currentFilters.seller = e.target.value;
        filterAndDisplayProducts();
    });

    searchInput.addEventListener('input', (e) => {
        currentFilters.search = e.target.value.toLowerCase();
        filterAndDisplayProducts();
    });

    statusFilter.addEventListener('change', (e) => {
        currentFilters.status = e.target.value;
        filterAndDisplayProducts();
    });

    proximityFilter.addEventListener('change', (e) => {
        currentFilters.proximity = e.target.value;
        filterAndDisplayProducts();
    });
}

// Filter products based on current filters
function filterAndDisplayProducts() {
    if (!priceData) return;

    filteredData = priceData.productGroups.filter(group => {
        // Filter by status
        if (currentFilters.status && group.groupAnalysis.groupStatus !== currentFilters.status) {
            return false;
        }

        // Filter by seller, search term, and proximity
        const hasMatchingProduct = group.products.some(product => {
            const seller = product.originalProductData.seller;
            const productName = product.originalProductData.productName?.toLowerCase() || '';
            const productNumber = product.originalProductData.productnumber?.toLowerCase() || '';
            const proximityStatus = product.priceAnalysis.priceStatus || 'close';

            // Check seller filter
            if (currentFilters.seller && seller !== currentFilters.seller) {
                return false;
            }

            // Check proximity filter
            if (currentFilters.proximity && proximityStatus !== currentFilters.proximity) {
                return false;
            }

            // Check search filter
            if (currentFilters.search) {
                return productName.includes(currentFilters.search) ||
                       productNumber.includes(currentFilters.search);
            }

            return true;
        });

        return hasMatchingProduct && group.products.length > 0;
    });

    displayProducts();
    updateResultsInfo();
}

// Display a specific selected group from search
function displaySelectedGroup(groupIndex) {
    if (!priceData || !priceData.productGroups || groupIndex >= priceData.productGroups.length) {
        console.error('Invalid group index:', groupIndex);
        filterAndDisplayProducts();
        return;
    }

    const selectedGroup = priceData.productGroups[groupIndex];
    filteredData = [selectedGroup];

    // Clear filters to show the selected group clearly
    currentFilters = { seller: '', search: '', status: '', proximity: '' };

    // Update filter UI
    sellerFilter.value = '';
    searchInput.value = '';
    statusFilter.value = '';
    proximityFilter.value = '';

    displayProducts();
    updateResultsInfo();

    // Add a notice that this is a selected group
    const notice = document.createElement('div');
    notice.className = 'selected-group-notice';
    notice.innerHTML = `
        <div style="background: #e3f2fd; border: 1px solid #2196f3; border-radius: 8px; padding: 15px; margin-bottom: 20px; text-align: center;">
            <strong>📍 Showing selected product group from search</strong>
            <button onclick="showAllGroups()" style="margin-left: 15px; padding: 8px 16px; background: #2196f3; color: white; border: none; border-radius: 4px; cursor: pointer;">
                Show All Groups
            </button>
        </div>
    `;
    productCards.insertBefore(notice, productCards.firstChild);
}

// Show all groups (remove search selection)
function showAllGroups() {
    const notice = document.querySelector('.selected-group-notice');
    if (notice) {
        notice.remove();
    }
    filterAndDisplayProducts();
}

// Display filtered products
function displayProducts() {
    if (!filteredData.length) {
        productCards.innerHTML = `
            <div style="grid-column: 1 / -1; text-align: center; padding: 40px; color: #666;">
                <h3>No products found</h3>
                <p>Try adjusting your search criteria or filters.</p>
            </div>
        `;
        return;
    }

    const cardsHTML = filteredData.map(group => createProductGroupCard(group)).join('');
    productCards.innerHTML = cardsHTML;
}

// Create HTML for a product group card
function createProductGroupCard(group) {
    const statusClass = group.groupAnalysis.groupStatus === 'close' ? 'status-close' : 'status-not-close';
    const statusText = group.groupAnalysis.groupStatus === 'close' ? 'Close' : 'Not Close';

    // Get a representative product name for the group
    const firstProduct = group.products[0];
    const groupName = firstProduct?.originalProductData.productName || 'Product Group';

    const productsHTML = group.products.map(product => createProductItem(product)).join('');

    return `
        <div class="product-group-card">
            <div class="group-header">
                <div class="group-status ${statusClass}">${statusText}</div>
                <div class="group-title">${truncateText(groupName, 60)}</div>
                <div class="group-info">
                    ${group.products.length} suppliers •
                    Max variance: ${group.groupAnalysis.maxPriceVariance.toFixed(1)}%
                </div>
            </div>
            <div class="products-list">
                ${productsHTML}
            </div>
        </div>
    `;
}

// Create HTML for individual product item
function createProductItem(product) {
    const data = product.originalProductData;
    const analysis = product.priceAnalysis;

    const isCheapest = analysis.isCheapest;
    const cheapestClass = isCheapest ? 'cheapest' : '';
    const cheapestBadge = isCheapest ? '<div class="cheapest-badge">Cheapest</div>' : '';

    // Product-level proximity status - only show if not cheapest (cheapest takes priority)
    const proximityStatus = analysis.priceStatus || 'close';
    const proximityClass = proximityStatus === 'close' ? 'price-close' : 'price-not-close';
    const proximityBadge = !isCheapest ? `<div class="proximity-badge ${proximityClass}">${proximityStatus === 'close' ? 'Close Price' : 'Not Close'}</div>` : '';

    const regularPrice = analysis.regularPrice > 0 ? `$${analysis.regularPrice.toFixed(2)}` : 'N/A';
    const portionPrice = analysis.portionPrice > 0 ? `$${analysis.portionPrice.toFixed(4)}` : 'N/A';
    const variance = analysis.priceVariancePercentage.toFixed(1);

    // Show comparison method used
    const comparisonMethod = analysis.comparisonMethod || 'standard';
    const methodBadge = `<div class="comparison-method-badge">${comparisonMethod.replace(/_/g, ' ')}</div>`;

    // Create comprehensive price display
    let allPricesHTML = '';
    if (analysis.allPriceProperties) {
        // Display portion prices
        if (analysis.allPriceProperties.portionPrices && Object.keys(analysis.allPriceProperties.portionPrices).length > 0) {
            allPricesHTML += '<div class="price-section"><strong>Portion Prices:</strong>';
            Object.entries(analysis.allPriceProperties.portionPrices).forEach(([prop, value]) => {
                allPricesHTML += `<div class="price-item">
                    <span class="price-property">${prop}:</span>
                    <span class="price-value">$${value.toFixed(4)}</span>
                </div>`;
            });
            allPricesHTML += '</div>';
        }

        // Display regular prices
        if (analysis.allPriceProperties.regularPrices && Object.keys(analysis.allPriceProperties.regularPrices).length > 0) {
            allPricesHTML += '<div class="price-section"><strong>Regular Prices:</strong>';
            Object.entries(analysis.allPriceProperties.regularPrices).forEach(([prop, value]) => {
                allPricesHTML += `<div class="price-item">
                    <span class="price-property">${prop}:</span>
                    <span class="price-value">$${value.toFixed(2)}</span>
                </div>`;
            });
            allPricesHTML += '</div>';
        }
    }

    return `
        <div class="product-item ${cheapestClass} ${proximityClass}">
            ${cheapestBadge}
            ${proximityBadge}
            ${methodBadge}
            <div class="product-name">${truncateText(data.productName || 'Unknown Product', 80)}</div>
            <div class="product-details">
                <div class="detail-item">
                    <span class="detail-label">Product #:</span>
                    <span>${data.productnumber || 'N/A'}</span>
                </div>
                <div class="detail-item">
                    <span class="detail-label">Unit Type:</span>
                    <span>${analysis.unittype || 'N/A'}</span>
                </div>
                <div class="detail-item">
                    <span class="detail-label">Comparison Price:</span>
                    <span class="price-highlight">${analysis.comparisonPrice !== null && analysis.comparisonPrice !== undefined ? '$' + analysis.comparisonPrice.toFixed(4) : 'N/A'}</span>
                </div>
                <div class="detail-item">
                    <span class="detail-label">Variance from Median:</span>
                    <span>${variance}%</span>
                </div>
                ${allPricesHTML}
            </div>
            <div class="supplier-badge">${data.seller.toUpperCase()}</div>
        </div>
    `;
}

// Update statistics display
function updateStatistics() {
    if (!priceData) return;

    const summary = priceData.summary;
    totalGroupsSpan.textContent = summary.totalGroups.toLocaleString();
    closeMatchesSpan.textContent = summary.closeMatches.toLocaleString();
    avgVarianceSpan.textContent = summary.averagePriceVariance.toFixed(1) + '%';
}

// Update results info
function updateResultsInfo() {
    showingGroupsSpan.textContent = filteredData.length.toLocaleString();

    if (currentFilters.seller || currentFilters.search || currentFilters.status || currentFilters.proximity) {
        resultsInfo.classList.remove('hidden');

        const filterDescriptions = [];
        if (currentFilters.seller) filterDescriptions.push(`supplier: ${currentFilters.seller}`);
        if (currentFilters.search) filterDescriptions.push(`search: "${currentFilters.search}"`);
        if (currentFilters.status) filterDescriptions.push(`group status: ${currentFilters.status}`);
        if (currentFilters.proximity) filterDescriptions.push(`product proximity: ${currentFilters.proximity}`);

        resultsText.textContent = `Showing ${filteredData.length} groups filtered by ${filterDescriptions.join(', ')}`;
    } else {
        resultsInfo.classList.add('hidden');
    }
}

// Utility function to truncate text
function truncateText(text, maxLength) {
    if (text.length <= maxLength) return text;
    return text.substring(0, maxLength - 3) + '...';
}

// Initialize the application when the page loads
document.addEventListener('DOMContentLoaded', initializeApp);
