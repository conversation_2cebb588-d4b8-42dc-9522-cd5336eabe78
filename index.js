// Search-first interface for price comparison
let priceData = null;
let searchIndex = [];

// DOM elements
const searchInput = document.getElementById('searchInput');
const searchResults = document.getElementById('searchResults');
const supplierFilter = document.getElementById('supplierFilter');
const statusFilter = document.getElementById('statusFilter');
const loading = document.getElementById('loading');
const noResults = document.getElementById('noResults');

// Initialize the application
async function init() {
    try {
        loading.style.display = 'block';
        await loadPriceData();
        buildSearchIndex();
        setupEventListeners();
        loading.style.display = 'none';
    } catch (error) {
        console.error('Failed to initialize:', error);
        loading.textContent = 'Failed to load price data. Please try again.';
    }
}

// Load price comparison data
async function loadPriceData() {
    try {
        const response = await fetch('price_comparison_results.json');
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        const data = await response.json();
        priceData = data.productGroups || [];
        console.log(`Loaded ${priceData.length} product groups`);
    } catch (error) {
        console.error('Error loading price data:', error);
        throw error;
    }
}

// Build search index for fast searching
function buildSearchIndex() {
    searchIndex = [];

    if (!priceData || !Array.isArray(priceData)) {
        console.error('Price data is not available or not an array:', priceData);
        return;
    }

    priceData.forEach((group, groupIndex) => {
        if (!group.products || !Array.isArray(group.products)) {
            console.warn(`Group ${groupIndex} has no products array:`, group);
            return;
        }

        group.products.forEach(product => {
            const data = product.originalProductData;
            const analysis = product.priceAnalysis;

            if (!data) {
                console.warn('Product missing originalProductData:', product);
                return;
            }

            // Create searchable text
            const searchText = [
                data.productName || '',
                data.name || '',
                data.productnumber || '',
                data.seller || ''
            ].join(' ').toLowerCase();

            searchIndex.push({
                groupIndex,
                product,
                searchText,
                productName: data.productName || data.name || 'Unknown Product',
                productNumber: data.productnumber || 'N/A',
                supplier: data.seller || 'Unknown',
                status: analysis.priceStatus || 'close',
                price: analysis.comparisonPrice || analysis.portionPrice || analysis.regularPrice || 0
            });
        });
    });

    console.log(`Built search index with ${searchIndex.length} products from ${priceData.length} groups`);
}

// Setup event listeners
function setupEventListeners() {
    searchInput.addEventListener('input', handleSearch);
    searchInput.addEventListener('focus', handleSearch);
    searchInput.addEventListener('blur', () => {
        // Delay hiding results to allow clicking
        setTimeout(() => {
            searchResults.style.display = 'none';
        }, 200);
    });

    supplierFilter.addEventListener('change', handleSearch);
    statusFilter.addEventListener('change', handleSearch);
}

// Handle search input
function handleSearch() {
    const query = searchInput.value.trim().toLowerCase();
    const supplier = supplierFilter.value;
    const status = statusFilter.value;

    console.log('Search triggered:', { query, supplier, status, indexSize: searchIndex.length });

    if (query.length < 2 && !supplier && !status) {
        searchResults.style.display = 'none';
        noResults.style.display = 'none';
        return;
    }

    // Filter search index with improved regex matching
    let filteredResults = searchIndex.filter(item => {
        // Text search with regex for better matching
        if (query.length >= 2) {
            const regex = new RegExp(query.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'i');
            if (!regex.test(item.searchText)) {
                return false;
            }
        }

        // Supplier filter
        if (supplier && item.supplier !== supplier) {
            return false;
        }

        // Status filter
        if (status && item.status !== status) {
            return false;
        }

        return true;
    });

    console.log('Filtered results:', filteredResults.length);

    // Sort by relevance (exact matches first, then by price)
    filteredResults.sort((a, b) => {
        if (query.length >= 2) {
            const aExact = a.productName.toLowerCase().includes(query) ? 1 : 0;
            const bExact = b.productName.toLowerCase().includes(query) ? 1 : 0;
            if (aExact !== bExact) return bExact - aExact;
        }
        return a.price - b.price;
    });

    // Limit results for performance
    filteredResults = filteredResults.slice(0, 50);

    displaySearchResults(filteredResults);
}

// Display search results
function displaySearchResults(results) {
    if (results.length === 0) {
        searchResults.style.display = 'none';
        noResults.style.display = 'block';
        return;
    }

    noResults.style.display = 'none';

    const html = results.map(item => {
        const priceDisplay = item.price > 0 ? `$${item.price.toFixed(2)}` : 'N/A';
        const statusClass = item.status === 'close' ? 'close' : 'not-close';

        return `
            <div class="search-result-item" onclick="selectProduct(${item.groupIndex})">
                <div class="result-name">${truncateText(item.productName, 60)}</div>
                <div class="result-details">
                    <span class="supplier-tag">${item.supplier.toUpperCase()}</span>
                    Product #: ${item.productNumber} |
                    Price: ${priceDisplay} |
                    Status: <span class="status-${statusClass}">${item.status}</span>
                </div>
            </div>
        `;
    }).join('');

    searchResults.innerHTML = html;
    searchResults.style.display = 'block';
}

// Select a product and navigate to its group
function selectProduct(groupIndex) {
    // Store the selected group index in localStorage
    localStorage.setItem('selectedGroupIndex', groupIndex);

    // Navigate to the main price comparison app
    window.location.href = 'price_comparison_app.html';
}

// Utility function to truncate text
function truncateText(text, maxLength) {
    if (!text) return '';
    return text.length > maxLength ? text.substring(0, maxLength) + '...' : text;
}

// Initialize when page loads
document.addEventListener('DOMContentLoaded', init);
