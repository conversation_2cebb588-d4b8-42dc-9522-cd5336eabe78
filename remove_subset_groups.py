#!/usr/bin/env python3
"""
Subset Group Removal <PERSON>

This script removes product groups that are complete subsets of larger groups
from matches_deduplicated.json. A group is removed if ALL of its product keys
exist within another larger group.

Usage: python remove_subset_groups.py
"""

import json
import logging
from typing import List, Dict, Set, Tuple
from collections import defaultdict
import time

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('subset_removal.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def load_data(filename: str) -> List[Dict[str, str]]:
    """Load the product groups data from JSON file."""
    try:
        logger.info(f"Loading data from {filename}...")
        with open(filename, 'r', encoding='utf-8') as file:
            data = json.load(file)
        logger.info(f"Successfully loaded {len(data)} product groups")
        return data
    except Exception as e:
        logger.error(f"Error loading file {filename}: {e}")
        raise

def get_group_keys(group: Dict[str, str]) -> Set[str]:
    """Extract product keys from a group as a set."""
    return set(group.keys())

def find_subset_groups(groups: List[Dict[str, str]]) -> Tuple[List[int], Dict[str, int]]:
    """
    Find all groups that are complete subsets of other groups.
    
    Returns:
        Tuple of (indices_to_remove, statistics)
    """
    logger.info("Starting subset analysis...")
    start_time = time.time()
    
    # Convert groups to sets for efficient comparison
    group_keysets = []
    for i, group in enumerate(groups):
        keyset = get_group_keys(group)
        group_keysets.append((i, keyset, len(keyset)))
    
    # Sort by size (smallest first) for efficient subset checking
    group_keysets.sort(key=lambda x: x[2])
    
    indices_to_remove = set()
    subset_relationships = []
    
    total_comparisons = 0
    
    # Check each group against all larger groups
    for i in range(len(group_keysets)):
        if i % 1000 == 0 and i > 0:
            logger.info(f"Processed {i}/{len(group_keysets)} groups for subset analysis")
        
        current_idx, current_keys, current_size = group_keysets[i]
        
        # Skip if this group is already marked for removal
        if current_idx in indices_to_remove:
            continue
        
        # Check against all larger groups
        for j in range(i + 1, len(group_keysets)):
            other_idx, other_keys, other_size = group_keysets[j]
            
            # Skip if the other group is already marked for removal
            if other_idx in indices_to_remove:
                continue
            
            total_comparisons += 1
            
            # Check if current group is a subset of the other group
            if current_keys.issubset(other_keys) and current_size < other_size:
                indices_to_remove.add(current_idx)
                subset_relationships.append({
                    'subset_index': current_idx,
                    'subset_size': current_size,
                    'superset_index': other_idx,
                    'superset_size': other_size,
                    'subset_keys': sorted(current_keys),
                    'superset_keys': sorted(other_keys)
                })
                logger.debug(f"Group {current_idx} (size {current_size}) is subset of group {other_idx} (size {other_size})")
                break  # Found a superset, no need to check further
    
    end_time = time.time()
    processing_time = end_time - start_time
    
    stats = {
        'total_groups': len(groups),
        'total_comparisons': total_comparisons,
        'subsets_found': len(indices_to_remove),
        'processing_time_seconds': processing_time,
        'subset_relationships': subset_relationships
    }
    
    logger.info(f"Subset analysis completed in {processing_time:.2f} seconds")
    logger.info(f"Total comparisons: {total_comparisons:,}")
    logger.info(f"Subset groups found: {len(indices_to_remove)}")
    
    return list(indices_to_remove), stats

def analyze_subset_samples(stats: Dict, max_samples: int = 10) -> None:
    """Analyze and log sample subset relationships."""
    relationships = stats['subset_relationships']
    
    if not relationships:
        logger.info("No subset relationships found")
        return
    
    logger.info(f"\nAnalyzing {min(len(relationships), max_samples)} sample subset relationships:")
    
    # Sort by size difference for interesting examples
    relationships.sort(key=lambda x: x['superset_size'] - x['subset_size'], reverse=True)
    
    for i, rel in enumerate(relationships[:max_samples]):
        logger.info(f"\nSample {i+1}:")
        logger.info(f"  Subset group {rel['subset_index']} ({rel['subset_size']} products)")
        logger.info(f"  Superset group {rel['superset_index']} ({rel['superset_size']} products)")
        logger.info(f"  Size difference: {rel['superset_size'] - rel['subset_size']} products")
        logger.info(f"  Subset keys: {rel['subset_keys'][:5]}{'...' if len(rel['subset_keys']) > 5 else ''}")

def remove_subset_groups(groups: List[Dict[str, str]]) -> Tuple[List[Dict[str, str]], Dict]:
    """
    Remove all groups that are complete subsets of other groups.
    
    Returns:
        Tuple of (filtered_groups, statistics)
    """
    # Find subset groups
    indices_to_remove, stats = find_subset_groups(groups)
    
    # Analyze samples
    analyze_subset_samples(stats)
    
    # Remove subset groups (in reverse order to maintain indices)
    filtered_groups = []
    removed_count = 0
    
    for i, group in enumerate(groups):
        if i not in indices_to_remove:
            filtered_groups.append(group)
        else:
            removed_count += 1
    
    # Update statistics
    stats.update({
        'original_count': len(groups),
        'filtered_count': len(filtered_groups),
        'removed_count': removed_count,
        'reduction_percentage': (removed_count / len(groups)) * 100 if groups else 0
    })
    
    logger.info(f"Removed {removed_count} subset groups")
    logger.info(f"Remaining groups: {len(filtered_groups)}")
    logger.info(f"Reduction: {stats['reduction_percentage']:.2f}%")
    
    return filtered_groups, stats

def save_results(data: List[Dict[str, str]], filename: str) -> None:
    """Save the filtered data to a JSON file."""
    try:
        logger.info(f"Saving filtered data to {filename}...")
        with open(filename, 'w', encoding='utf-8') as file:
            json.dump(data, file, indent=2, ensure_ascii=False)
        logger.info(f"Successfully saved {len(data)} groups to {filename}")
    except Exception as e:
        logger.error(f"Error saving file {filename}: {e}")
        raise

def validate_results(original_groups: List[Dict[str, str]], filtered_groups: List[Dict[str, str]]) -> bool:
    """Validate that no subset groups remain in the filtered data."""
    logger.info("Validating results...")
    
    # Convert to keysets for validation
    filtered_keysets = [get_group_keys(group) for group in filtered_groups]
    
    subset_found = False
    for i in range(len(filtered_keysets)):
        for j in range(len(filtered_keysets)):
            if i != j:
                if filtered_keysets[i].issubset(filtered_keysets[j]) and len(filtered_keysets[i]) < len(filtered_keysets[j]):
                    logger.error(f"Validation failed: Group {i} is still a subset of group {j}")
                    subset_found = True
    
    if not subset_found:
        logger.info("✅ Validation PASSED: No subset groups found in filtered data")
        return True
    else:
        logger.error("❌ Validation FAILED: Subset groups still exist")
        return False

def main():
    """Main function to orchestrate the subset removal process."""
    input_file = 'matches_deduplicated.json'
    output_file = 'matches_subset_cleaned.json'
    stats_file = 'subset_removal_stats.json'
    
    try:
        # Load data
        groups = load_data(input_file)
        
        # Remove subset groups
        filtered_groups, stats = remove_subset_groups(groups)
        
        # Validate results
        validation_passed = validate_results(groups, filtered_groups)
        stats['validation_passed'] = validation_passed
        
        # Save results
        save_results(filtered_groups, output_file)
        
        # Save statistics
        with open(stats_file, 'w', encoding='utf-8') as f:
            json.dump(stats, f, indent=2, default=str)
        logger.info(f"Statistics saved to {stats_file}")
        
        # Final summary
        logger.info(f"\n=== SUBSET REMOVAL SUMMARY ===")
        logger.info(f"Input file: {input_file}")
        logger.info(f"Output file: {output_file}")
        logger.info(f"Original groups: {stats['original_count']:,}")
        logger.info(f"Filtered groups: {stats['filtered_count']:,}")
        logger.info(f"Subset groups removed: {stats['removed_count']:,}")
        logger.info(f"Reduction: {stats['reduction_percentage']:.2f}%")
        logger.info(f"Processing time: {stats['processing_time_seconds']:.2f} seconds")
        logger.info(f"Validation: {'PASSED' if validation_passed else 'FAILED'}")
        
    except Exception as e:
        logger.error(f"Script failed: {e}")
        raise

if __name__ == "__main__":
    main()
