import { GoogleGenAI } from '@google/genai';
import * as fs from 'fs';
import * as dotenv from 'dotenv';
dotenv.config();
  
  async function main() {


    const ai = new GoogleGenAI({
      apiKey: process.env.GEMINI_API_KEY,
    });
    const config = {

      responseMimeType: 'application/json',
      systemInstruction: [
          {
            text: `You are an expert product matching system. Your goal is to identify suitable alternative products from a "Matching Catalog" for products listed in a "Source Catalog", focusing on functional equivalence rather than exact size or quantity matches. Imagine a scenario where a user needs a replacement for a Source Catalog product that is out of stock. The best Matching Catalog match should be something the user could use instead of the original.
  Input: You will receive a JSON array. Each object in the array contains two main sections: sourceCatalogProduct and matchingCatalogMatches. sourceCatalogProduct contains a single entry with a Source Catalog product identifier (e.g., product number, SKU) as the key and the product name as the value. matchingCatalogMatches contains a list of potential matching products from the Matching Catalog, with product identifiers as keys and product names as values.
  Task: Analyze the product names in both sections and determine which Matching Catalog products are the best functional matches for the Source Catalog product. Follow these rules:
  Analyze Source Product: Extract the key characteristics of the Source Catalog product from its name. Identify:
  Core Product Category: (e.g., Fruit, Vegetable, Meat, Dairy, Baking Ingredient, Beverage, Cleaning Supply, Food Container, Tool, Apparel, Electronics). This is the most important factor. Think broadly but accurately.
  Product Type: (e.g., Apple, Chicken Breast, Garlic, Sugar, Plate, Hammer, T-shirt, Toaster). This should be a specific type within the Core Product Category.
  Product Form/Condition: (e.g., Sliced, Diced, Whole, Frozen, Fresh, Ground, Canned, Dried, Cooked, Raw, Powdered, Liquid, Solid). Also critical to match when possible. Pay close attention to descriptors like "Ready-to-Eat".
  Flavor Profile/Variety/Strain: (e.g., Pasilla, Granny Smith, Mild, Spicy, Arabica, Indica). Important when present, especially for food, beverages, and some agricultural products. Identify if it is a blend or single-origin.
  Material/Composition: (e.g., Stainless Steel, Wood, Plastic, China, Cotton, Polyester). Important for non-food items and packaging. If a product is coated or infused with something (e.g., "Garlic Infused Olive Oil"), identify both the base material and the coating/infusion.
  Packaging: (e.g. Can, Bottle, Bag, Case, Box, Tub, Pouch, Tray, Wrapped). Consider but less important than the previous factors. Note the type of packaging material (e.g. glass, plastic, cardboard).
  Certifications/Claims: (e.g., Organic, Gluten-Free, Kosher, Halal, Vegan, Non-GMO). These are important to match if possible, but not strictly required.
  Brand/Manufacturer: Note the brand if present, but do not prioritize matching it unless the product type is very generic (e.g., "Paper Towels").
  Evaluate Matching Matches: For each potential match in the matchingCatalogMatches section:
  Prioritize Core Category, Type and Form/Condition: The Matching Catalog product must belong to the same Core Product Category as the Source product. It should also ideally match the Product Type and Form/Condition. If any of these are significantly different, it's NOT a good match. For example, you would not match a chicken breast with ground beef, nor sliced apples with whole apples. You also would not want to match a food product with equipment (chicken breast with a food container). A slight change in form is acceptable(Fresh with frozen).
  Ignore Size/Weight/Quantity (Unless Relevant to the Product Type): For nearly all food products, ingredients, and consumables, differences in size, weight, or quantity are irrelevant. A customer can simply adjust the amount they purchase. Exception: If the product is a container, utensil, equipment, small appliance, clothing size, or supply where physical dimensions are important, then size can be relevant, but still consider a range of possible sizes to be a match.
  Expand Abbreviations: Assume that product names use abbreviations. Use common sense and your knowledge of general goods to expand these before comparing (e.g., "DFRCP" = "Disposable Food Container", "WRP" = "Wood Rolling Pin", "ct" = "count", "oz" = "ounce", "lb" = "pound", "ltr" = "liter", "S/S" = "Stainless Steel", "ea" = each).
  Account for Synonyms and Similar Terms: Recognize that different vendors may use different terms to describe the same product. For example, "Chili" and "Pepper" can be interchangeable in some contexts. "Napkins" and "Serviettes" are the same.
  Correcting Typos and Misinformation: Recognize that product names may have typos. Try to correct for these mistakes and assess the correct product. If something is drastically wrong, skip it.
  Select Matches: Include only the Matching Catalog products that represent the best functional alternatives to the Source Catalog product, based on the criteria above.
  Output JSON: Return a JSON array containing JSON objects in the following format. The matchingCatalogMatches object should only contain the product identifiers and names of the validated matches.
  Important Considerations:
  Focus on Replaceability: The most important consideration is whether the Matching Catalog product could realistically be used as a replacement for the Source Catalog product if the latter was unavailable.
  Be Strict: It is better to return an empty matchingCatalogMatches object than to include incorrect matches.
  No Explanations: Do not include any introductory text, explanations, or any other text besides the JSON object(s) within the array. Just the JSON.
  Array Output: Remember that your output must be a JSON array containing one or more JSON objects . they should have same sructure and seller name as the original input.
  For example, if the source catalog is "chefProduct" and the matching catalog is "matches" in the input it should stay the same in the output:
  [
    {
      "chefProduct": {
        "51213": "James Farm - Medium Loose Eggs - 15 Dozen"
      },
      "matches": {
        "3193571": "Egg, Fresh Medium Grade Aa 1/2 Cs 144\\" Cage Size P12nc",
        "3933571": "Egg, Fresh Medium Aa Loose Flat 144\\" Cage Size",
        "4852091": "Egg, Fresh Medium Grade Aa White Loose Pack P12ce"
      }
    }
  ]`,
          }
      ],
    };
    const model = 'gemini-2.0-flash';
    const contents = [
      {
        role: 'user',
        parts: [
          {
            text: `[
    {
      "chefProduct": {
        "51213": "James Farm - Medium Loose Eggs - 15 Dozen"
      },
      "matches": {
        "3193571": "Egg, Fresh Medium Grade Aa 1/2 Cs 144\\" Cage Size P12nc",
        "3933571": "Egg, Fresh Medium Aa Loose Flat 144\\" Cage Size",
        "4852091": "Egg, Fresh Medium Grade Aa White Loose Pack P12ce"
      }
    },
    {
      "chefProduct": {
        "51215": "James Farm - Grade A Extra Large Loose Eggs - 15 Dozen"
      },
      "matches": {
        "1030361": "Egg, Fresh Extra Large Aa 1/2 Cs 144\\" Cage Size",
        "1032261": "Egg, Fresh Large A Carton 144\\" Cage Size",
        "1656401": "Mayonnaise, Extra Heavy Egg Yolk 4 Gal Pail Shelf Stable",
        "1926551": "Sour Cream, Extra Hold",
        "1926561": "Sour Cream, Extra Hold",
        "1959311": "Egg, Fresh Large Aa Loose 1/2cs 144\\" Cage Size",
        "1959321": "Egg, Fresh Extra Large Aa Carton 144\\" Cage Size",
        "2028981": "Noodle, Egg Extra Wide Bulk 7/8\\"",
        "2154861": "Fries, Extra Long Fancy Steak Skin On Coated Grade A Raw Bag Frozen",
        "3193571": "Egg, Fresh Medium Grade Aa 1/2 Cs 144\\" Cage Size P12nc",
        "3452661": "Asparagus, Extra Large",
        "3566851": "Fries, Extra Long Fancy Shoestring 1/4\\" Skin On Coated Grade A Raw Bag Frozen",
        "3933561": "Egg, Fresh Large Aa Loose Flat 144\\" Cage Size",
        "3933571": "Egg, Fresh Medium Aa Loose Flat 144\\" Cage Size",
        "4596901": "Egg, Fresh Large Grade Aa White Cage Free Loose Pack 15 Dz Refrigerated P12ce",
        "4717381": "Fries, Extra Long Fancy Crinkle Cut 1/2\\" Skin Off Coated Grade A Raw Bag Frozen",
        "4735141": "Fries, Extra Long Fancy Entree Cut 1/4\\" Skin On Battered Bent Arm Ale Grade A Raw Bag Frozen",
        "4849681": "Fries, Extra Long Fancy Crinkle Cut 5/16\\" Grade A Thin",
        "4849751": "Fries, Extra Long Fancy Shoestring 1/4\\" Coated Grade A Marathon",
        "4849761": "Fries, Extra Long Fancy Shoestring 1/4\\" Grade A",
        "4849891": "Fries, Extra Long Fancy Straight Cut 3/8\\" Coated Grade A Marathon",
        "4849921": "Fries, Extra Long Fancy Straight Cut 5/16\\" Coated Grade A Thin Marathon",
        "4849931": "Fries, Extra Long Fancy Straight Cut 3/8\\" Skin On Grade A Frozen",
        "4852091": "Egg, Fresh Medium Grade Aa White Loose Pack P12ce",
        "4862531": "Fries, Extra Long Fancy Straight Cut 3/8\\" Skin On Coated Grade A Raw Bag Frozen"
      }
    },
    {
      "chefProduct": {
        "51211": "James Farm - Large Loose White Eggs - 15 Dozen"
      },
      "matches": {
        "4596901": "Egg, Fresh Large Grade Aa White Cage Free Loose Pack 15 Dz Refrigerated P12ce"
      }
    },
    {
      "chefProduct": {
        "32625": "James Farm - Jumbo Loose Eggs - 16.7 Dozen"
      },
      "matches": {
        "1030361": "Egg, Fresh Extra Large Aa 1/2 Cs 144\\" Cage Size",
        "1032261": "Egg, Fresh Large A Carton 144\\" Cage Size",
        "1413941": "Egg, Fresh Quail Carton Refrigerated Uzura",
        "1959311": "Egg, Fresh Large Aa Loose 1/2cs 144\\" Cage Size",
        "1959321": "Egg, Fresh Extra Large Aa Carton 144\\" Cage Size",
        "2553191": "Egg, Cooked Hard Whole Peeled Bag",
        "2698481": "Egg, Cooked Hard Diced Iqf",
        "2863651": "Egg, Cooked Hard Whole Peeled Table Ready",
        "2876111": "Egg, Cooked Hard Peeled Premium Select 144\\" Cage Size",
        "2876131": "Egg, Cooked Hard Peeled Premium Select 144\\" Cage Size",
        "3193571": "Egg, Fresh Medium Grade Aa 1/2 Cs 144\\" Cage Size P12nc",
        "3742171": "Egg White, Highwhip Frozen Cage Free",
        "3742181": "Egg White, Liquid Cage Free",
        "3933561": "Egg, Fresh Large Aa Loose Flat 144\\" Cage Size",
        "3933571": "Egg, Fresh Medium Aa Loose Flat 144\\" Cage Size",
        "4594071": "Egg White, Liquid Cage Free W-Stabilizer",
        "4596901": "Egg, Fresh Large Grade Aa White Cage Free Loose Pack 15 Dz Refrigerated P12ce",
        "4596971": "Egg, Liquid Whole W-Citric Acid 144\\" Cage Size",
        "4852091": "Egg, Fresh Medium Grade Aa White Loose Pack P12ce",
        "4897071": "Egg, Cooked Hard Peeled",
        "4954511": "Egg, Liquid Whole Cage Free Citric Acid Esl P12ce",
        "4954531": "Egg, Liquid Whole Cage Free Citric Acid Bag Refrigerated Esl P12ce",
        "4988851": "Egg, Cooked Hard Peeled Cage Free 2pk",
        "0029138": "Egg Sub, Plant Based Fried Sunny Side Up Vegan Bag Frozen",
        "0029141": "Egg Sub, Plant Based Poached Bag Frozen"
      }
    },
    {
      "chefProduct": {
        "77232": "Boneless, Skinless Chicken Breasts, Tenders Out, Dry"
      },
      "matches": {
        "1755671": "Chicken, Breast Single 6 Oz Boneless Skinless Tender Out Natural Gourmet Trimmed",
        "1790561": "Chicken, Breast Single 4 Oz Boneless Skinless Tender Out Natural Refrigerated Gourmet Trimmed",
        "1790591": "Chicken, Breast Single 8 Oz Boneless Skinless Tender Out Natural Gourmet Trimmed",
        "1906571": "Chicken, Breast Boneless & Skinless Tender Pressed",
        "1907491": "Chicken, Breast Boneless & Skinless Tender Pressed Savory",
        "2559591": "Chicken, Breast Double 6 Oz Boneless Skinless Tender Out Natural Vacuum Pack Refrigerated Flattened",
        "3176221": "Chicken, Breast Single Boneless & Skinless To Solution Added 6oz",
        "3230891": "Chicken, Breast Boneless & Skinless Tender Pressed",
        "3321131": "Chicken, Breast Double 6 Oz Boneless Skinless Tender Out Natural Vacuum Pack Refrigerated Flattened",
        "4075871": "Chicken, Breast Single 6 Oz Boneless Skinless Tender Out Natural Refrigerated Cubed",
        "4093901": "Chicken, Breast Single 6 Oz Boneless Skinless Tender Out Nae Natural Refrigerated",
        "4093961": "Chicken, Breast Single 8 Oz Boneless Skinless Tender Out Nae Natural Refrigerated",
        "4130811": "Chicken, Breast Single 4 Oz Boneless Skinless Tender Out Natural Refrigerated Gourmet Trimmed",
        "4135681": "Chicken, Breast Single 5 Oz Boneless Skinless Tender Out Nae Natural Refrigerated",
        "4181821": "Chicken, Breast Single 7 Oz Boneless Skinless Tender Out Nae Natural Refrigerated Flattened",
        "4261081": "Chicken, Breast Single 4 Oz Boneless Skinless Tender Out Nae Natural Refrigerated",
        "4264911": "Chicken, Breast Single 4 Oz Boneless Skinless Tender Out Natural Refrigerated Cubed",
        "4279391": "Chicken, Breast Single 6 Oz Boneless Skinless Tender Out Nae Natural Flattened",
        "4361591": "Chicken, Breast Single 5 Oz Boneless Skinless Tender Out Natural Refrigerated Gourmet Trimmed",
        "4754571": "Chicken, Breast Single 4 Oz Boneless Skinless Tender Out Marinated 15% Natural Vacuum Pack Iqf W-Rib Meat Jumbo Cut Down",
        "4754581": "Chicken, Breast Single 5 Oz Boneless Skinless Tender Out Marinated 15% Natural Vacuum Pack Iqf W-Rib Meat Jumbo Cut Down",
        "4754601": "Chicken, Breast Single 6 Oz Boneless Skinless Tender Out Marinated 15% Natural Vacuum Pack Iqf W-Rib Meat Jumbo Cut Down",
        "0040127": "Chicken, Breast Double Random Boneless Skinless Tender In Halal Nae Fresh To Frozen Extended Shelf Life",
        "0039693": "Chicken, Breast Double Random Boneless Skinless Tender In Halal Nae Refrigerated Extended Shelf Life",
        "0028346": "Chicken, Breast Single 5 Oz Boneless Skinless Tender Out Natural Tray Pack Refrigerated Trimmed Cubed"
      }
    },
    {
      "chefProduct": {
        "44146": "Peeled Garlic"
      },
      "matches": {
        "1070941": "Garlic, Whole Head Bulk Refrigerated Fresh Jumbo",
        "2709681": "Garlic, Roasted Peeled",
        "3013741": "Garlic, Clove Peeled Bag Refrigerated Fresh",
        "3013743": "Garlic, Clove Peeled Bag Refrigerated Fresh",
        "4370841": "Garlic, Black Clove Peeled Refrigerated Fresh"
      }
    },
    {
      "chefProduct": {
        "79926": "Always Fresh Beef - 0x1 Strip Loin, USDA Select"
      },
      "matches": {
        "1435751": "Beef, Strip Loin 0x1 Sterling Silver",
        "1565281": "Beef, Strip Loin 12 Up Prime Angus 180 0x1 Boneless Refrigerated",
        "1648051": "Beef, Strip Loin Choice 180 0x1 Refrigerated Ups",
        "1784551": "Beef, Strip Loin 0x1 13dn Gold Canyon Angus",
        "1784571": "Beef, Strip Loin Whole 0x1 Gold Canyon Angus",
        "1804621": "Beef, Strip Loin 0x1 12-15lb Choice",
        "3100531": "Beef, Strip Loin 0x1 Down Gold Canyon Angus",
        "3100891": "Beef, Strip Loin 13.5 Dn Choice Angus 180 0x1 Boneless Refrigerated",
        "3100901": "Beef, Strip Loin 12.5 Up Choice Angus 180 0x1 Boneless Vacuum Pack Refrigerated",
        "3118871": "Beef, Strip Loin Bone In 0x1 Gold Canyon Angus",
        "3128191": "Beef, Strip Loin 0x1 Extra Trim Cdr Rvr Prime",
        "3266951": "Beef, Strip Loin 0x1 Evr (Emerald Valley Ranch)",
        "3266961": "Beef, Strip Loin 0x1 Evr (Emerald Valley Ranch)",
        "3591811": "Beef, Strip Loin 0x1 Down Gold Canyon Angus",
        "3628771": "Beef, Strip Loin 0x1 Gca (Gold Canyon Angus)",
        "3752841": "Beef, Strip Loin 0x1 Evr (Emerald Valley Ranch)",
        "3921841": "Beef, Strip Loin 13 Up Choice Angus 180 .25\\" Trimmed Boneless Vacuum Pack Refrigerated Backstrap Off",
        "3924221": "Beef, Strip Loin Choice 180 0x1 Boneless Refrigerated",
        "3991351": "Beef, Strip Loin Bone In 0x1 Gold Canyon Angus",
        "4378611": "Beef, Strip Loin 0x1 Angus No Vein Gold Canyon Angus",
        "4629571": "Beef, Strip Loin 0x1 Gold Canyon Angus",
        "4669991": "Beef, Strip Loin Prime Angus 0x1",
        "4672431": "Beef, Strip Loin Prime Angus 0x1",
        "5057691": "Beef, Strip Loin 0x1 Elite Prime",
        "0032077": "Beef, Strip Loin Choice 0x1 Fresh To Frozen Grass Run"
      }
    }]`,
          },
        ],
      },
      {
        role: 'model',
        parts: [
          {
            text: `[
    {
      "chefProduct": {
        "51213": "James Farm - Medium Loose Eggs - 15 Dozen"
      },
      "matches": {
        "3193571": "Egg, Fresh Medium Grade Aa 1/2 Cs 144\\" Cage Size P12nc",
        "3933571": "Egg, Fresh Medium Aa Loose Flat 144\\" Cage Size",
        "4852091": "Egg, Fresh Medium Grade Aa White Loose Pack P12ce"
      }
    },
    {
      "chefProduct": {
        "51215": "James Farm - Grade A Extra Large Loose Eggs - 15 Dozen"
      },
      "matches": {
        "1030361": "Egg, Fresh Extra Large Aa 1/2 Cs 144\\" Cage Size",
        "1959321": "Egg, Fresh Extra Large Aa Carton 144\\" Cage Size"
      }
    },
    {
      "chefProduct": {
        "51211": "James Farm - Large Loose White Eggs - 15 Dozen"
      },
      "matches": {
        "4596901": "Egg, Fresh Large Grade Aa White Cage Free Loose Pack 15 Dz Refrigerated P12ce"
      }
    },
    {
      "chefProduct": {
        "32625": "James Farm - Jumbo Loose Eggs - 16.7 Dozen"
      },
      "matches": {}
    },
    {
      "chefProduct": {
        "77232": "Boneless, Skinless Chicken Breasts, Tenders Out, Dry"
      },
      "matches": {
        "1755671": "Chicken, Breast Single 6 Oz Boneless Skinless Tender Out Natural Gourmet Trimmed",
        "1790561": "Chicken, Breast Single 4 Oz Boneless Skinless Tender Out Natural Refrigerated Gourmet Trimmed",
        "1790591": "Chicken, Breast Single 8 Oz Boneless Skinless Tender Out Natural Gourmet Trimmed",
        "1906571": "Chicken, Breast Boneless & Skinless Tender Pressed",
        "3230891": "Chicken, Breast Boneless & Skinless Tender Pressed",
        "4093901": "Chicken, Breast Single 6 Oz Boneless Skinless Tender Out Nae Natural Refrigerated",
        "4093961": "Chicken, Breast Single 8 Oz Boneless Skinless Tender Out Nae Natural Refrigerated",
        "4130811": "Chicken, Breast Single 4 Oz Boneless Skinless Tender Out Natural Refrigerated Gourmet Trimmed",
        "4135681": "Chicken, Breast Single 5 Oz Boneless Skinless Tender Out Nae Natural Refrigerated",
        "4261081": "Chicken, Breast Single 4 Oz Boneless Skinless Tender Out Nae Natural Refrigerated",
        "4361591": "Chicken, Breast Single 5 Oz Boneless Skinless Tender Out Natural Refrigerated Gourmet Trimmed",
        "0039693": "Chicken, Breast Double Random Boneless Skinless Tender In Halal Nae Refrigerated Extended Shelf Life"
      }
    },
    {
      "chefProduct": {
        "44146": "Peeled Garlic"
      },
      "matches": {
        "3013741": "Garlic, Clove Peeled Bag Refrigerated Fresh",
        "3013743": "Garlic, Clove Peeled Bag Refrigerated Fresh"
      }
    },
    {
      "chefProduct": {
        "79926": "Always Fresh Beef - 0x1 Strip Loin, USDA Select"
      },
      "matches": {
        "1435751": "Beef, Strip Loin 0x1 Sterling Silver",
        "1565281": "Beef, Strip Loin 12 Up Prime Angus 180 0x1 Boneless Refrigerated",
        "1648051": "Beef, Strip Loin Choice 180 0x1 Refrigerated Ups",
        "1784551": "Beef, Strip Loin 0x1 13dn Gold Canyon Angus",
        "1784571": "Beef, Strip Loin Whole 0x1 Gold Canyon Angus",
        "1804621": "Beef, Strip Loin 0x1 12-15lb Choice",
        "3100531": "Beef, Strip Loin 0x1 Down Gold Canyon Angus",
        "3100891": "Beef, Strip Loin 13.5 Dn Choice Angus 180 0x1 Boneless Refrigerated",
        "3100901": "Beef, Strip Loin 12.5 Up Choice Angus 180 0x1 Boneless Vacuum Pack Refrigerated",
        "3118871": "Beef, Strip Loin Bone In 0x1 Gold Canyon Angus",
        "3128191": "Beef, Strip Loin 0x1 Extra Trim Cdr Rvr Prime",
        "3266951": "Beef, Strip Loin 0x1 Evr (Emerald Valley Ranch)",
        "3266961": "Beef, Strip Loin 0x1 Evr (Emerald Valley Ranch)",
        "3591811": "Beef, Strip Loin 0x1 Down Gold Canyon Angus",
        "3628771": "Beef, Strip Loin 0x1 Gca (Gold Canyon Angus)",
        "3752841": "Beef, Strip Loin 0x1 Evr (Emerald Valley Ranch)",
        "3921841": "Beef, Strip Loin 13 Up Choice Angus 180 .25\\" Trimmed Boneless Vacuum Pack Refrigerated Backstrap Off",
        "3924221": "Beef, Strip Loin Choice 180 0x1 Boneless Refrigerated",
        "3991351": "Beef, Strip Loin Bone In 0x1 Gold Canyon Angus",
        "4378611": "Beef, Strip Loin 0x1 Angus No Vein Gold Canyon Angus",
        "4629571": "Beef, Strip Loin 0x1 Gold Canyon Angus",
        "4669991": "Beef, Strip Loin Prime Angus 0x1",
        "4672431": "Beef, Strip Loin Prime Angus 0x1",
        "5057691": "Beef, Strip Loin 0x1 Elite Prime",
        "0032077": "Beef, Strip Loin Choice 0x1 Fresh To Frozen Grass Run"
      }
    }
  ]`,
          },
        ],
      },
      {
        role: 'user',
        parts: [
          {
            text: `INSERT_INPUT_HERE`,
          },
        ],
      },
    ];
  
    
  let outputData = [];
  if (fs.existsSync('chef_R.json')) {
    outputData = JSON.parse(fs.readFileSync('chef_R.json', 'utf8'));
  } else {
    fs.writeFileSync('chef_R.json', '[]', 'utf8');
  }

  let lastKey = null;
  if (outputData.length > 0) {
    const lastObject = outputData[outputData.length - 1];
    const sourceCatalog = Object.keys(lastObject)[0];
    lastKey = Object.keys(lastObject[sourceCatalog])[0];
  }

  const inputData = fs.readFileSync('chef.json', 'utf8');
  const inputArray = JSON.parse(inputData);

  let startIndex = 0;
  if (lastKey) {
    for (let i = 0; i < inputArray.length; i++) {
      const sourceCatalog = Object.keys(inputArray[i])[0];
      const currentKey = Object.keys(inputArray[i][sourceCatalog])[0];
      if (currentKey === lastKey) {
        startIndex = i + 1;
        break;
      }
    }
  }

  const batchSize = 6;
  const totalBatches = Math.ceil(inputArray.length / batchSize);
  for (let i = startIndex; i < inputArray.length; i += batchSize) {
    console.log(`Processing batch ${Math.floor(i / batchSize) + 1} out of ${totalBatches}`);
    const batch = inputArray.slice(i, i + batchSize);
    const batchStr = JSON.stringify(batch);
    contents[2].parts[0].text = batchStr;

    const response = await ai.models.generateContentStream({
      model,
      config,
      contents,
    });
    let fullResponse = '';
    for await (const chunk of response) {
      fullResponse += chunk.text;
    }
    const responseArray = JSON.parse(fullResponse);
    outputData.push(...responseArray);
    fs.writeFileSync('chef_R.json', JSON.stringify(outputData, null, 2), 'utf8');
    await new Promise(resolve => setTimeout(resolve, 10000));
  }
}

main();