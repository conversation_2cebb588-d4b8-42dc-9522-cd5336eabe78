<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Price Comparison System</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f5f5f5;
            color: #333;
        }

        .container {
            display: flex;
            min-height: 100vh;
        }

        /* Sidebar Styles */
        .sidebar {
            width: 300px;
            background: white;
            box-shadow: 2px 0 10px rgba(0,0,0,0.1);
            padding: 20px;
            position: fixed;
            height: 100vh;
            overflow-y: auto;
            z-index: 1000;
        }

        .sidebar h2 {
            color: #2c3e50;
            margin-bottom: 20px;
            font-size: 1.5rem;
        }

        .filter-section {
            margin-bottom: 25px;
        }

        .filter-section label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #555;
        }

        .filter-section select,
        .filter-section input {
            width: 100%;
            padding: 10px;
            border: 2px solid #e0e0e0;
            border-radius: 6px;
            font-size: 14px;
            transition: border-color 0.3s ease;
        }

        .filter-section select:focus,
        .filter-section input:focus {
            outline: none;
            border-color: #3498db;
        }

        .stats-section {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            margin-top: 20px;
        }

        .stats-section h3 {
            color: #2c3e50;
            margin-bottom: 10px;
            font-size: 1.1rem;
        }

        .stat-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 5px;
            font-size: 14px;
        }

        /* Main Content Styles */
        .main-content {
            flex: 1;
            margin-left: 300px;
            padding: 20px;
        }

        .header {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }

        .header h1 {
            color: #2c3e50;
            margin-bottom: 10px;
        }

        .header p {
            color: #666;
            font-size: 16px;
        }

        .results-info {
            background: #e8f4fd;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            border-left: 4px solid #3498db;
        }

        .loading {
            text-align: center;
            padding: 40px;
            color: #666;
            font-size: 18px;
        }

        /* Product Card Styles */
        .product-cards {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
            gap: 20px;
        }

        .product-group-card {
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            overflow: hidden;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .product-group-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0,0,0,0.15);
        }

        .group-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px 20px;
            position: relative;
        }

        .group-status {
            position: absolute;
            top: 10px;
            right: 15px;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
        }

        .status-close {
            background: rgba(46, 204, 113, 0.2);
            color: #27ae60;
            border: 1px solid rgba(46, 204, 113, 0.3);
        }

        .status-not-close {
            background: rgba(231, 76, 60, 0.2);
            color: #e74c3c;
            border: 1px solid rgba(231, 76, 60, 0.3);
        }

        .group-title {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 5px;
        }

        .group-info {
            font-size: 14px;
            opacity: 0.9;
        }

        .products-list {
            padding: 0;
        }

        .product-item {
            padding: 15px 20px;
            border-bottom: 1px solid #f0f0f0;
            position: relative;
        }

        .product-item:last-child {
            border-bottom: none;
        }

        .product-item.cheapest {
            background: linear-gradient(90deg, rgba(46, 204, 113, 0.05) 0%, rgba(46, 204, 113, 0.02) 100%);
            border-left: 4px solid #2ecc71;
        }

        .cheapest-badge {
            position: absolute;
            top: 10px;
            left: 15px;
            background: #2ecc71;
            color: white;
            padding: 4px 10px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 700;
            text-transform: uppercase;
            z-index: 3;
            box-shadow: 0 2px 4px rgba(46, 204, 113, 0.3);
        }

        .product-name {
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 8px;
            font-size: 14px;
            line-height: 1.3;
        }

        .product-details {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
            font-size: 13px;
            color: #666;
        }

        .detail-item {
            display: flex;
            justify-content: space-between;
        }

        .detail-label {
            font-weight: 500;
        }

        .price-highlight {
            color: #e74c3c;
            font-weight: 600;
        }

        .supplier-badge {
            display: inline-block;
            background: #3498db;
            color: white;
            padding: 2px 8px;
            border-radius: 10px;
            font-size: 11px;
            font-weight: 600;
            text-transform: uppercase;
            margin-top: 5px;
        }

        /* Product-level proximity badges */
        .proximity-badge {
            position: absolute;
            top: 10px;
            right: 15px;
            padding: 3px 8px;
            border-radius: 12px;
            font-size: 10px;
            font-weight: 600;
            text-transform: uppercase;
            z-index: 2;
        }

        .proximity-badge.price-close {
            background: #27ae60;
            color: white;
        }

        .proximity-badge.price-not-close {
            background: #e74c3c;
            color: white;
        }

        /* Price type badges for multi-option suppliers */
        .price-type-badge {
            position: absolute;
            top: 8px;
            left: 8px;
            background: #9b59b6;
            color: white;
            padding: 2px 6px;
            border-radius: 8px;
            font-size: 9px;
            font-weight: 600;
            text-transform: uppercase;
            z-index: 2;
        }

        /* Product item styling based on proximity */
        .product-item.price-not-close {
            border-left: 4px solid #e74c3c;
            opacity: 0.85;
        }

        .product-item.price-close {
            border-left: 4px solid #27ae60;
        }

        /* Comparison method badges */
        .comparison-method-badge {
            position: absolute;
            bottom: 8px;
            left: 8px;
            background: #34495e;
            color: white;
            padding: 2px 6px;
            border-radius: 8px;
            font-size: 9px;
            font-weight: 600;
            text-transform: capitalize;
            z-index: 2;
        }

        /* Comprehensive price display */
        .price-section {
            margin-top: 10px;
            padding: 8px;
            background: #f8f9fa;
            border-radius: 4px;
            border: 1px solid #e9ecef;
        }

        .price-section strong {
            color: #495057;
            font-size: 12px;
            display: block;
            margin-bottom: 5px;
        }

        .price-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 3px 5px;
            margin: 2px 0;
            border-radius: 3px;
            font-size: 11px;
        }

        .price-item.selected-price {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            font-weight: 600;
        }

        .price-property {
            color: #6c757d;
            font-weight: 500;
        }

        .price-value {
            color: #495057;
            font-weight: 600;
        }

        .used-indicator {
            color: #28a745;
            font-size: 10px;
            font-weight: 700;
            margin-left: 5px;
        }

        /* Adjust product item positioning for new badges */
        .product-item {
            position: relative;
            padding-bottom: 35px; /* Make room for comparison method badge */
        }

        /* Search link styling */
        .search-link {
            display: inline-block;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 12px 20px;
            border-radius: 25px;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
            text-align: center;
            width: 100%;
            box-sizing: border-box;
        }

        .search-link:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
            text-decoration: none;
            color: white;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .sidebar {
                width: 100%;
                height: auto;
                position: relative;
            }

            .main-content {
                margin-left: 0;
            }

            .product-cards {
                grid-template-columns: 1fr;
            }

            .product-details {
                grid-template-columns: 1fr;
            }
        }

        /* Utility Classes */
        .hidden {
            display: none;
        }

        .text-center {
            text-align: center;
        }

        .mb-10 {
            margin-bottom: 10px;
        }

        .mt-20 {
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Sidebar -->
        <div class="sidebar">
            <h2>🔍 Search & Filter</h2>

            <div class="filter-section">
                <label for="sellerFilter">Filter by Supplier:</label>
                <select id="sellerFilter">
                    <option value="">All Suppliers</option>
                    <option value="chef">Chef</option>
                    <option value="sham">Sham</option>
                    <option value="sysco">Sysco</option>
                    <option value="usfood">US Food</option>
                    <option value="greco">Greco</option>
                    <option value="depot">Depot</option>
                    <option value="perf">Perf</option>
                </select>
            </div>

            <div class="filter-section">
                <label for="searchInput">Search Products:</label>
                <input type="text" id="searchInput" placeholder="Product name or number...">
            </div>

            <div class="filter-section">
                <label for="statusFilter">Group Status:</label>
                <select id="statusFilter">
                    <option value="">All Groups</option>
                    <option value="close">Close Groups (≤40%)</option>
                    <option value="not close">Not Close Groups (>40%)</option>
                </select>
            </div>

            <div class="filter-section">
                <label for="proximityFilter">Product Proximity:</label>
                <select id="proximityFilter">
                    <option value="">All Products</option>
                    <option value="close">Close Price Products</option>
                    <option value="not close">Not Close Price Products</option>
                </select>
            </div>

            <div class="filter-section">
                <a href="index.html" class="search-link">🔍 Product Search</a>
            </div>

            <div class="stats-section">
                <h3>📊 Statistics</h3>
                <div class="stat-item">
                    <span>Total Groups:</span>
                    <span id="totalGroups">-</span>
                </div>
                <div class="stat-item">
                    <span>Showing:</span>
                    <span id="showingGroups">-</span>
                </div>
                <div class="stat-item">
                    <span>Close Matches:</span>
                    <span id="closeMatches">-</span>
                </div>
                <div class="stat-item">
                    <span>Avg. Variance:</span>
                    <span id="avgVariance">-</span>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="main-content">
            <div class="header">
                <h1>🛒 Price Comparison System</h1>
                <p>Compare prices across multiple suppliers and find the best deals for your products.</p>
            </div>

            <div id="resultsInfo" class="results-info hidden">
                <strong>Search Results:</strong> <span id="resultsText"></span>
            </div>

            <div id="loadingMessage" class="loading">
                Loading price comparison data...
            </div>

            <div id="productCards" class="product-cards hidden">
                <!-- Product cards will be dynamically generated here -->
            </div>
        </div>
    </div>

    <script src="price_comparison_app.js"></script>
</body>
</html>
