#!/usr/bin/env python3
"""
Verification script to confirm subset removal worked correctly.
"""

import json

def verify_subset_removal():
    print("Loading files...")
    
    # Load original deduplicated file
    with open('matches_deduplicated.json', 'r', encoding='utf-8') as f:
        original = json.load(f)
    
    # Load subset cleaned file  
    with open('matches_subset_cleaned.json', 'r', encoding='utf-8') as f:
        cleaned = json.load(f)
    
    print(f"Original groups (deduplicated): {len(original):,}")
    print(f"Subset cleaned groups: {len(cleaned):,}")
    print(f"Groups removed: {len(original) - len(cleaned):,}")
    print(f"Reduction percentage: {((len(original) - len(cleaned)) / len(original)) * 100:.2f}%")
    
    # Check for remaining subset relationships
    print("\nChecking for remaining subset relationships...")
    
    # Convert to keysets for efficient comparison
    cleaned_keysets = []
    for i, group in enumerate(cleaned):
        keyset = set(group.keys())
        cleaned_keysets.append((i, keyset, len(keyset)))
    
    # Sort by size for efficient checking
    cleaned_keysets.sort(key=lambda x: x[2])
    
    subset_found = False
    subset_count = 0
    
    for i in range(len(cleaned_keysets)):
        current_idx, current_keys, current_size = cleaned_keysets[i]
        
        for j in range(i + 1, len(cleaned_keysets)):
            other_idx, other_keys, other_size = cleaned_keysets[j]
            
            # Check if current is subset of other
            if current_keys.issubset(other_keys) and current_size < other_size:
                subset_found = True
                subset_count += 1
                if subset_count <= 5:  # Show first 5 examples
                    print(f"Subset found: Group {current_idx} (size {current_size}) ⊂ Group {other_idx} (size {other_size})")
    
    print(f"Total subset relationships remaining: {subset_count}")
    
    if not subset_found:
        print("✅ Verification PASSED: No subset relationships found in cleaned file")
    else:
        print("❌ Verification FAILED: Subset relationships still exist")
    
    # Check file size reduction
    original_size = 59935102  # From previous file
    cleaned_size = 20596996   # From current file
    size_reduction = ((original_size - cleaned_size) / original_size) * 100
    print(f"\nFile size reduction: {size_reduction:.2f}%")
    print(f"Original file size: {original_size:,} bytes")
    print(f"Cleaned file size: {cleaned_size:,} bytes")
    
    return not subset_found

if __name__ == "__main__":
    verify_subset_removal()
