#!/usr/bin/env python3
"""
Verification script to confirm deduplication worked correctly.
"""

import json

def verify_deduplication():
    print("Loading files...")
    
    # Load original file
    with open('matches_cleaned.json', 'r', encoding='utf-8') as f:
        original = json.load(f)
    
    # Load deduplicated file  
    with open('matches_deduplicated.json', 'r', encoding='utf-8') as f:
        deduplicated = json.load(f)
    
    print(f"Original groups: {len(original):,}")
    print(f"Deduplicated groups: {len(deduplicated):,}")
    print(f"Groups removed: {len(original) - len(deduplicated):,}")
    print(f"Reduction percentage: {((len(original) - len(deduplicated)) / len(original)) * 100:.2f}%")
    
    # Check for remaining duplicates
    print("\nChecking for remaining duplicates...")
    signatures = set()
    duplicates_found = 0
    
    for i, group in enumerate(deduplicated):
        signature = frozenset(group.keys())
        if signature in signatures:
            duplicates_found += 1
            print(f"Duplicate found at index {i}")
        signatures.add(signature)
    
    print(f"Duplicates remaining: {duplicates_found} (should be 0)")
    
    if duplicates_found == 0:
        print("✅ Verification PASSED: No duplicates found in deduplicated file")
    else:
        print("❌ Verification FAILED: Duplicates still exist")
    
    return duplicates_found == 0

if __name__ == "__main__":
    verify_deduplication()
